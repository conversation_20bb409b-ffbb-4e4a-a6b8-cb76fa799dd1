APP_NAME=cms
APP_ENV=local
APP_KEY=base64:cUyVr8VR/XIUtfdMAXEQjr5ilZs0ww6X0iRI0tI+6Kg=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=cms
DB_USERNAME=gerardo
DB_PASSWORD=password

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

FILESYSTEM_DISK=local

CACHE_STORE=redis
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

JWT_SECRET=sMBKwWDoCQTg4h4xeGEEyelDaKmehyCJmp1Gt1IEuPyvPMFeo9Vh0utw9cF6uARs
JWT_TTL=20160
JWT_ALGO=HS256

LARAVEL_ECHO_SERVER_ID=bed44b5922ef7e5c
LARAVEL_ECHO_SERVER_KEY=2b17b5dde66d953f6769a227d6fab0a7

BROADCAST_DRIVER=redis
QUEUE_CONNECTION=redis


PUSHER_APP_ID=bed44b5922ef7e5c
PUSHER_APP_KEY=2b17b5dde66d953f6769a227d6fab0a7
PUSHER_APP_SECRET=local
PUSHER_CLUSTER=mt1
PUSHER_HOST=127.0.0.1
PUSHER_PORT=6001
PUSHER_SCHEME=http

CACHE_DRIVER=redis

LARAVEL_ECHO_SERVER_AUTH_HOST=http://localhost
LARAVEL_ECHO_SERVER_PORT=6001
LARAVEL_ECHO_SERVER_DEBUG=true