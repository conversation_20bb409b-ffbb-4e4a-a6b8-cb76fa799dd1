<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Patient;
use App\Models\Department;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use PHPOpenSourceSaver\JWTAuth\Facades\JWTAuth;

class QueueHistoryTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $department;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test department
        $this->department = Department::create([
            'name' => 'Test Department',
            'description' => 'Test Department Description',
            'status' => 'available'
        ]);

        // Create a test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'department_id' => $this->department->id,
            'role_id' => 1
        ]);
    }

    public function test_queue_history_requires_authentication()
    {
        $response = $this->getJson('/api/patients/queue/history');
        $response->assertStatus(401);
    }

    public function test_queue_history_returns_empty_array_when_no_patients()
    {
        $token = JWTAuth::fromUser($this->user);
        
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/patients/queue/history');

        $response->assertStatus(200)
                 ->assertJson([]);
    }

    public function test_queue_history_returns_patients_for_today()
    {
        $token = JWTAuth::fromUser($this->user);

        // Create a test patient
        $patient = Patient::create([
            'priority_number' => 1,
            'name' => 'Test Patient',
            'priority' => 'Regular',
            'status' => 'completed',
            'starting_department_id' => $this->department->id,
            'next_department_id' => $this->department->id,
            'session_started' => now(),
            'session_ended' => now()->addHour(),
        ]);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/patients/queue/history');

        $response->assertStatus(200)
                 ->assertJsonCount(1)
                 ->assertJsonFragment([
                     'name' => 'Test Patient',
                     'priority' => 'Regular',
                     'status' => 'completed'
                 ]);
    }

    public function test_queue_history_filters_by_date()
    {
        $token = JWTAuth::fromUser($this->user);

        // Create patients for different dates
        $yesterday = now()->subDay();
        $today = now();

        Patient::create([
            'priority_number' => 1,
            'name' => 'Yesterday Patient',
            'priority' => 'Regular',
            'status' => 'completed',
            'starting_department_id' => $this->department->id,
            'created_at' => $yesterday,
            'updated_at' => $yesterday,
        ]);

        Patient::create([
            'priority_number' => 2,
            'name' => 'Today Patient',
            'priority' => 'Regular',
            'status' => 'completed',
            'starting_department_id' => $this->department->id,
            'created_at' => $today,
            'updated_at' => $today,
        ]);

        // Test filtering by yesterday's date
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/patients/queue/history?date=' . $yesterday->format('Y-m-d'));

        // Debug: Let's see what we actually get
        $responseData = $response->json();
        dump('Yesterday response:', $responseData);
        dump('User department IDs:', $this->user->getAllDepartmentIds());

        $response->assertStatus(200)
                 ->assertJsonCount(1)
                 ->assertJsonFragment(['name' => 'Yesterday Patient']);

        // Test filtering by today's date
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/patients/queue/history?date=' . $today->format('Y-m-d'));

        $response->assertStatus(200)
                 ->assertJsonCount(1)
                 ->assertJsonFragment(['name' => 'Today Patient']);
    }

    public function test_queue_history_validates_date_format()
    {
        $token = JWTAuth::fromUser($this->user);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/patients/queue/history?date=invalid-date');

        $response->assertStatus(400)
                 ->assertJsonFragment(['message' => 'Invalid date format. Use YYYY-MM-DD.']);
    }
}
